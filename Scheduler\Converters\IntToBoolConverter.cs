// 引入必要的命名空间 / Import necessary namespaces
using System.Globalization; // 全球化支持 / Globalization support

namespace Scheduler.Converters
{
    /// <summary>
    /// 整数转布尔值转换器 / Integer to Boolean Converter
    /// 将整数转换为布尔值（大于0为true）
    /// Converts integer to boolean value (greater than 0 returns true)
    /// </summary>
    public class IntToBoolConverter : IValueConverter
    {
        /// <summary>
        /// 将整数转换为布尔值 / Convert Integer to Boolean
        /// 大于0的整数返回true，否则返回false
        /// Returns true for integers greater than 0, otherwise false
        /// </summary>
        /// <param name="value">整数值 / Integer value</param>
        /// <param name="targetType">目标类型 / Target type</param>
        /// <param name="parameter">参数（未使用）/ Parameter (unused)</param>
        /// <param name="culture">文化信息 / Culture info</param>
        /// <returns>如果整数大于0返回true，否则返回false / Returns true if integer > 0, otherwise false</returns>
        public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is int intValue)
            {
                return intValue > 0;
            }

            // 尝试转换其他数值类型 / Try to convert other numeric types
            if (value != null && int.TryParse(value.ToString(), out int parsedValue))
            {
                return parsedValue > 0;
            }

            return false; // 默认返回false / Default return false
        }

        /// <summary>
        /// 反向转换方法 / Convert Back Method
        /// 将布尔值转换回整数（true为1，false为0）
        /// Converts boolean value back to integer (true to 1, false to 0)
        /// </summary>
        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? 1 : 0; // true转换为1，false转换为0 / Convert true to 1, false to 0
            }
            return 0; // 默认返回0 / Default return 0
        }
    }
}
