// 引入必要的命名空间 / Import necessary namespaces
using System.Globalization; // 全球化支持 / Globalization support

namespace Scheduler.Converters
{
    /// <summary>
    /// 字符串转布尔值转换器 / String to Boolean Converter
    /// 用于判断字符串是否为空，非空返回true
    /// Used to determine if string is empty, returns true for non-empty strings
    /// </summary>
    public class StringToBoolConverter : IValueConverter
    {
        /// <summary>
        /// 转换方法 / Convert Method
        /// 检查字符串是否不为空或空白
        /// Checks if string is not null or whitespace
        /// </summary>
        /// <param name="value">输入的字符串值 / Input string value</param>
        /// <param name="targetType">目标类型 / Target type</param>
        /// <param name="parameter">转换参数 / Conversion parameter</param>
        /// <param name="culture">文化信息 / Culture info</param>
        /// <returns>字符串非空返回true，否则返回false / Returns true if string is not empty, otherwise false</returns>
        public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            return !string.IsNullOrWhiteSpace(value?.ToString()); // 检查字符串是否有内容 / Check if string has content
        }

        /// <summary>
        /// 反向转换方法 / Convert Back Method
        /// 此转换器不支持反向转换
        /// This converter does not support reverse conversion
        /// </summary>
        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
