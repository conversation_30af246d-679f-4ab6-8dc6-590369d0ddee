// 引入必要的命名空间 / Import necessary namespaces
using System.Globalization; // 全球化支持 / Globalization support

namespace Scheduler.Converters
{
    /// <summary>
    /// 布尔值到颜色选择转换器 / Boolean to Color Selection Converter
    /// 将布尔值转换为颜色选择的边框颜色和宽度
    /// Converts boolean value to border color and width for color selection
    /// </summary>
    public class BoolToColorSelectionConverter : IValueConverter
    {
        /// <summary>
        /// 转换方法 / Convert Method
        /// 根据选中状态和参数返回不同的视觉属性
        /// Returns different visual properties based on selection state and parameter
        /// </summary>
        /// <param name="value">输入的布尔值 / Input boolean value</param>
        /// <param name="targetType">目标类型 / Target type</param>
        /// <param name="parameter">转换参数 / Conversion parameter</param>
        /// <param name="culture">文化信息 / Culture info</param>
        /// <returns>转换后的值 / Converted value</returns>
        public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is bool isSelected && isSelected)
            {
                // 选中时返回较粗的边框 / Return thicker border when selected
                if (parameter?.ToString() == "BorderWidth")
                    return 4.0;
                else if (parameter?.ToString() == "BorderColor")
                    return Color.FromArgb("#007ACC"); // 蓝色边框 / Blue border
                else if (parameter?.ToString() == "CheckmarkVisibility")
                    return true; // 显示勾选标记 / Show checkmark
            }

            // 未选中时返回细边框或透明 / Return thin border or transparent when not selected
            if (parameter?.ToString() == "BorderWidth")
                return 1.0; // 细边框 / Thin border
            else if (parameter?.ToString() == "BorderColor")
                return Color.FromArgb("#E5E7EB"); // 灰色边框 / Gray border
            else if (parameter?.ToString() == "CheckmarkVisibility")
                return false; // 隐藏勾选标记 / Hide checkmark

            return false;
        }

        /// <summary>
        /// 反向转换方法 / Convert Back Method
        /// 此转换器不支持反向转换
        /// This converter does not support reverse conversion
        /// </summary>
        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
