// 引入必要的命名空间 / Import necessary namespaces
using System.Globalization; // 全球化支持 / Globalization support

namespace Scheduler.Converters
{
    /// <summary>
    /// 布尔值到颜色转换器 / Boolean to Color Converter
    /// 将布尔值转换为颜色，用于高亮显示今天的日期
    /// Converts boolean to color for highlighting today's date
    /// </summary>
    public class BoolToColorConverter : IValueConverter
    {
        /// <summary>
        /// 转换方法 / Convert Method
        /// 将布尔值转换为对应的颜色
        /// Converts boolean value to corresponding color
        /// </summary>
        /// <param name="value">输入的布尔值 / Input boolean value</param>
        /// <param name="targetType">目标类型 / Target type</param>
        /// <param name="parameter">转换参数 / Conversion parameter</param>
        /// <param name="culture">文化信息 / Culture info</param>
        /// <returns>转换后的颜色 / Converted color</returns>
        public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is bool isToday && isToday)
            {
                return Color.FromArgb("#E3F2FD"); // 今天的浅蓝色背景 / Light blue background for today
            }
            return Colors.Transparent; // 透明背景 / Transparent background
        }

        /// <summary>
        /// 反向转换方法 / Convert Back Method
        /// 此转换器不支持反向转换
        /// This converter does not support reverse conversion
        /// </summary>
        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
