// 引入必要的命名空间 / Import necessary namespaces
using System.Globalization; // 全球化支持 / Globalization support

namespace Scheduler.Converters
{
    /// <summary>
    /// 布尔值到字体属性转换器 / <PERSON>olean to Font Attributes Converter
    /// 将布尔值转换为字体属性，用于高亮显示今天的日期
    /// Converts boolean to font attributes for highlighting today's date
    /// </summary>
    public class BoolToFontAttributesConverter : IValueConverter
    {
        /// <summary>
        /// 转换方法 / Convert Method
        /// 根据布尔值返回相应的字体属性
        /// Returns corresponding font attributes based on boolean value
        /// </summary>
        /// <param name="value">输入的布尔值 / Input boolean value</param>
        /// <param name="targetType">目标类型 / Target type</param>
        /// <param name="parameter">转换参数 / Conversion parameter</param>
        /// <param name="culture">文化信息 / Culture info</param>
        /// <returns>字体属性 / Font attributes</returns>
        public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is bool isToday && isToday)
            {
                return FontAttributes.Bold; // 今天的日期使用粗体 / Use bold for today's date
            }
            return FontAttributes.None; // 其他日期使用普通字体 / Use normal font for other dates
        }

        /// <summary>
        /// 反向转换方法 / Convert Back Method
        /// 此转换器不支持反向转换
        /// This converter does not support reverse conversion
        /// </summary>
        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
