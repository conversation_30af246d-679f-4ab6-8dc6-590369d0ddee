// 引入必要的命名空间 / Import necessary namespaces
using System.Globalization; // 全球化支持 / Globalization support

namespace Scheduler.Converters
{
    /// <summary>
    /// 集合数量转布尔值转换器 / Count to Boolean Converter
    /// 用于显示空状态，将数量转换为布尔值
    /// Used for displaying empty state, converts count to boolean value
    /// </summary>
    public class CountToBoolConverter : IValueConverter
    {
        /// <summary>
        /// 转换方法 / Convert Method
        /// 将数量转换为布尔值（0为true，其他为false）
        /// Converts count to boolean value (0 returns true, others return false)
        /// </summary>
        /// <param name="value">输入的数量值 / Input count value</param>
        /// <param name="targetType">目标类型 / Target type</param>
        /// <param name="parameter">转换参数 / Conversion parameter</param>
        /// <param name="culture">文化信息 / Culture info</param>
        /// <returns>布尔值 / Boolean value</returns>
        public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is int count)
            {
                return count == 0; // 数量为0时返回true（显示空状态） / Return true when count is 0 (show empty state)
            }
            return true; // 默认返回true / Default return true
        }

        /// <summary>
        /// 反向转换方法 / Convert Back Method
        /// 此转换器不支持反向转换
        /// This converter does not support reverse conversion
        /// </summary>
        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
