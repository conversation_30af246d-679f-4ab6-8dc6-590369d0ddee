// 引入必要的命名空间 / Import necessary namespaces
using System.Globalization; // 全球化支持 / Globalization support
using Scheduler.Models;      // 数据模型 / Data models

namespace Scheduler.Converters
{
    /// <summary>
    /// 分钟数转索引转换器 / Minutes to Index Converter
    /// 用于班次提醒时间选择，将分钟数转换为选择器索引
    /// Used for shift reminder time selection, converts minutes to picker index
    /// </summary>
    public class MinutesToIndexConverter : IValueConverter
    {
        // 预定义的分钟选项 / Predefined minute options
        private readonly int[] _minuteOptions = { 5, 10, 15, 30, 60 };

        /// <summary>
        /// 转换方法 / Convert Method
        /// 将分钟数转换为对应的索引
        /// Converts minutes to corresponding index
        /// </summary>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int minutes)
            {
                for (int i = 0; i < _minuteOptions.Length; i++)
                {
                    if (_minuteOptions[i] == minutes)
                        return i;
                }
            }
            return 2; // 默认选择15分钟（索引2）/ Default select 15 minutes (index 2)
        }

        /// <summary>
        /// 反向转换方法 / Convert Back Method
        /// 将索引转换回分钟数
        /// Converts index back to minutes
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int index && index >= 0 && index < _minuteOptions.Length)
            {
                return _minuteOptions[index];
            }
            return 15; // 默认15分钟 / Default 15 minutes
        }
    }

    /// <summary>
    /// 小时数转索引转换器（用于休息提醒间隔选择）
    /// </summary>
    public class HoursToIndexConverter : IValueConverter
    {
        private readonly int[] _hourOptions = { 1, 2, 3, 4 };

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int hours)
            {
                for (int i = 0; i < _hourOptions.Length; i++)
                {
                    if (_hourOptions[i] == hours)
                        return i;
                }
            }
            return 1; // 默认选择2小时（索引1）
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int index && index >= 0 && index < _hourOptions.Length)
            {
                return _hourOptions[index];
            }
            return 2; // 默认2小时
        }
    }

    /// <summary>
    /// 天数转索引转换器（用于支付提醒提前天数选择）
    /// </summary>
    public class DaysToIndexConverter : IValueConverter
    {
        private readonly int[] _dayOptions = { 0, 1, 2, 3, 7 };

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int days)
            {
                for (int i = 0; i < _dayOptions.Length; i++)
                {
                    if (_dayOptions[i] == days)
                        return i;
                }
            }
            return 1; // 默认选择1天前（索引1）
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int index && index >= 0 && index < _dayOptions.Length)
            {
                return _dayOptions[index];
            }
            return 1; // 默认1天前
        }
    }





    /// <summary>
    /// 时间转字符串转换器（用于免打扰时间显示）
    /// </summary>
    public class TimeToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is TimeSpan time)
            {
                return time.ToString(@"hh\:mm");
            }
            return "00:00";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string timeString && TimeSpan.TryParse(timeString, out var time))
            {
                return time;
            }
            return TimeSpan.Zero;
        }
    }

    /// <summary>
    /// 通知类型转描述转换器
    /// </summary>
    public class NotificationTypeToDescriptionConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Models.NotificationType type)
            {
                return type switch
                {
                    Models.NotificationType.WorkReminder => "工作提醒",
                    Models.NotificationType.BreakReminder => "休息提醒",
                    Models.NotificationType.PaymentDue => "支付提醒",
                    Models.NotificationType.ShiftConflict => "班次冲突",
                    Models.NotificationType.SystemUpdate => "系统更新",
                    _ => "未知类型"
                };
            }
            return "未知类型";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 通知状态转颜色转换器
    /// </summary>
    public class NotificationStatusToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Models.NotificationStatus status)
            {
                return status switch
                {
                    Models.NotificationStatus.Pending => Colors.Orange,
                    Models.NotificationStatus.Sent => Colors.Green,
                    Models.NotificationStatus.Read => Colors.Blue,
                    Models.NotificationStatus.Dismissed => Colors.Gray,
                    _ => Colors.Black
                };
            }
            return Colors.Black;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 通知类型到字符串转换器
    /// </summary>
    public class NotificationTypeToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is NotificationType type)
            {
                return type switch
                {
                    NotificationType.WorkReminder => "工作提醒",
                    NotificationType.BreakReminder => "休息提醒",
                    NotificationType.PaymentDue => "支付提醒",
                    NotificationType.ShiftConflict => "班次冲突",
                    NotificationType.SystemUpdate => "系统更新",
                    _ => "未知类型"
                };
            }

            if (value == null)
            {
                return "全部类型";
            }

            return value.ToString() ?? "未知";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 通知状态到字符串转换器
    /// </summary>
    public class NotificationStatusToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is NotificationStatus status)
            {
                return status switch
                {
                    NotificationStatus.Pending => "待发送",
                    NotificationStatus.Sent => "已发送",
                    NotificationStatus.Read => "已读",
                    NotificationStatus.Dismissed => "已忽略",
                    _ => "未知状态"
                };
            }

            if (value == null)
            {
                return "全部状态";
            }

            return value.ToString() ?? "未知";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 通知类型到颜色转换器
    /// </summary>
    public class NotificationTypeToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is NotificationType type)
            {
                return type switch
                {
                    NotificationType.WorkReminder => Color.FromArgb("#4CAF50"), // 绿色
                    NotificationType.BreakReminder => Color.FromArgb("#2196F3"), // 蓝色
                    NotificationType.PaymentDue => Color.FromArgb("#FF9800"), // 橙色
                    NotificationType.ShiftConflict => Color.FromArgb("#F44336"), // 红色
                    NotificationType.SystemUpdate => Color.FromArgb("#9C27B0"), // 紫色
                    _ => Color.FromArgb("#757575") // 灰色
                };
            }

            return Color.FromArgb("#757575");
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 通知状态到未读可见性转换器
    /// </summary>
    public class NotificationStatusToUnreadVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is NotificationStatus status)
            {
                return status != NotificationStatus.Read;
            }

            return false;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 反转布尔值转换器
    /// </summary>
    public class InvertedBoolConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }

            return true;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }

            return false;
        }
    }
}
