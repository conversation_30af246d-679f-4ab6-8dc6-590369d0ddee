// 引入必要的命名空间 / Import necessary namespaces
using System.Globalization; // 全球化支持 / Globalization support

namespace Scheduler.Converters
{
    /// <summary>
    /// 布尔值反转转换器 / Boolean Inverse Converter
    /// 将true转换为false，false转换为true
    /// Converts true to false and false to true
    /// </summary>
    /// <remarks>
    /// 常用于XAML数据绑定中需要反转布尔值的场景，例如：
    /// Commonly used in XAML data binding scenarios where boolean values need to be inverted, such as:
    /// - 控件的IsVisible属性需要与ViewModel中的布尔属性相反 / Control's IsVisible property needs to be opposite to boolean property in ViewModel
    /// - 按钮的IsEnabled状态需要与某个条件相反 / Button's IsEnabled state needs to be opposite to a certain condition
    /// - 支持双向绑定，ConvertBack方法同样执行反转操作 / Supports two-way binding, ConvertBack method also performs inversion
    /// </remarks>
    public class InverseBoolConverter : IValueConverter
    {
        /// <summary>
        /// 将布尔值转换为其反值
        /// Convert boolean value to its inverse
        /// </summary>
        /// <param name="value">要转换的布尔值</param>
        /// <param name="targetType">目标类型（通常为bool）</param>
        /// <param name="parameter">转换参数（未使用）</param>
        /// <param name="culture">文化信息（未使用）</param>
        /// <returns>反转后的布尔值，非布尔值时返回true</returns>
        public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }
            return true; // 默认值：当输入不是布尔值时返回true
        }

        /// <summary>
        /// 将反转后的布尔值转换回原值（双向绑定支持）
        /// Convert inverted boolean value back to original (for two-way binding)
        /// </summary>
        /// <param name="value">要反转回来的布尔值</param>
        /// <param name="targetType">目标类型（通常为bool）</param>
        /// <param name="parameter">转换参数（未使用）</param>
        /// <param name="culture">文化信息（未使用）</param>
        /// <returns>再次反转后的布尔值，非布尔值时返回false</returns>
        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }
            return false; // 默认值：当输入不是布尔值时返回false
        }
    }
}
