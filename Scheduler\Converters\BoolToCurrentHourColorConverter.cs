// 引入必要的命名空间 / Import necessary namespaces
using System.Globalization; // 全球化支持 / Globalization support

namespace Scheduler.Converters
{
    /// <summary>
    /// 布尔值到当前小时颜色转换器 / Boolean to Current Hour Color Converter
    /// 将布尔值转换为颜色，用于高亮显示当前小时
    /// Converts boolean to color for highlighting current hour
    /// </summary>
    public class BoolToCurrentHourColorConverter : IValueConverter
    {
        /// <summary>
        /// 转换方法 / Convert Method
        /// 根据是否为当前小时返回不同的颜色
        /// Returns different colors based on whether it's the current hour
        /// </summary>
        /// <param name="value">输入的布尔值 / Input boolean value</param>
        /// <param name="targetType">目标类型 / Target type</param>
        /// <param name="parameter">转换参数 / Conversion parameter</param>
        /// <param name="culture">文化信息 / Culture info</param>
        /// <returns>转换后的颜色 / Converted color</returns>
        public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is bool isCurrentHour && isCurrentHour)
            {
                return Color.FromArgb("#3B82F6"); // 当前小时的蓝色 / Blue color for current hour
            }
            return Color.FromArgb("#6B7280"); // 其他小时的灰色 / Gray color for other hours
        }

        /// <summary>
        /// 反向转换方法 / Convert Back Method
        /// 此转换器不支持反向转换
        /// This converter does not support reverse conversion
        /// </summary>
        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
