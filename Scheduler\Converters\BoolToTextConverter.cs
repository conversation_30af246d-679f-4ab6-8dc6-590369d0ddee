// 引入必要的命名空间 / Import necessary namespaces
using System.Globalization; // 全球化支持 / Globalization support

namespace Scheduler.Converters
{
    /// <summary>
    /// 布尔值转文本转换器 / Boolean to Text Converter
    /// 根据布尔值和参数返回不同的文本
    /// Returns different text based on boolean value and parameter
    /// </summary>
    public class BoolToTextConverter : IValueConverter
    {
        /// <summary>
        /// 将布尔值转换为文本 / Convert Boolean to Text
        /// 根据布尔值和参数字符串返回相应的文本
        /// Returns corresponding text based on boolean value and parameter string
        /// </summary>
        /// <param name="value">布尔值 / Boolean value</param>
        /// <param name="targetType">目标类型 / Target type</param>
        /// <param name="parameter">参数格式："false文本|true文本" / Parameter format: "false text|true text"</param>
        /// <param name="culture">文化信息 / Culture info</param>
        /// <returns>对应的文本 / Corresponding text</returns>
        public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is bool boolValue && parameter is string parameterString)
            {
                var texts = parameterString.Split('|');
                if (texts.Length == 2)
                {
                    return boolValue ? texts[1] : texts[0]; // true返回第二个文本，false返回第一个文本 / Return second text for true, first text for false
                }
            }

            // 默认返回布尔值的字符串表示 / Default return string representation of boolean value
            return value?.ToString() ?? "false";
        }

        /// <summary>
        /// 反向转换方法 / Convert Back Method
        /// 此转换器不支持反向转换
        /// This converter does not support reverse conversion
        /// </summary>
        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
