// 引入必要的命名空间 / Import necessary namespaces
using System.Globalization; // 全球化支持 / Globalization support

namespace Scheduler.Converters
{
    /// <summary>
    /// 布尔值到文本颜色转换器 / Boolean to Text Color Converter
    /// 将布尔值转换为文本颜色，用于区分当前月份和其他月份的日期
    /// Converts boolean to text color for current month vs other month dates
    /// </summary>
    public class BoolToTextColorConverter : IValueConverter
    {
        /// <summary>
        /// 转换方法 / Convert Method
        /// 根据是否为当前月份返回不同的文本颜色
        /// Returns different text colors based on whether it's the current month
        /// </summary>
        /// <param name="value">输入的布尔值 / Input boolean value</param>
        /// <param name="targetType">目标类型 / Target type</param>
        /// <param name="parameter">转换参数 / Conversion parameter</param>
        /// <param name="culture">文化信息 / Culture info</param>
        /// <returns>文本颜色 / Text color</returns>
        public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is bool isCurrentMonth && isCurrentMonth)
            {
                return Color.FromArgb("#1F2937"); // 当前月份的深色文本 / Dark text for current month
            }
            return Color.FromArgb("#9CA3AF"); // 其他月份的浅灰色文本 / Light gray for other months
        }

        /// <summary>
        /// 反向转换方法 / Convert Back Method
        /// 此转换器不支持反向转换
        /// This converter does not support reverse conversion
        /// </summary>
        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
