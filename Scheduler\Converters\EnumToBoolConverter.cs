// 引入必要的命名空间 / Import necessary namespaces
using System.Globalization; // 全球化支持 / Globalization support

namespace Scheduler.Converters
{
    /// <summary>
    /// 枚举值转布尔值转换器 / Enum to Boolean Converter
    /// 将枚举值与参数进行比较，返回布尔值
    /// Compares enum value with parameter and returns boolean value
    /// </summary>
    public class EnumToBoolConverter : IValueConverter
    {
        /// <summary>
        /// 转换方法 / Convert Method
        /// 比较枚举值与参数是否相等
        /// Compares if enum value equals the parameter
        /// </summary>
        /// <param name="value">输入的枚举值 / Input enum value</param>
        /// <param name="targetType">目标类型 / Target type</param>
        /// <param name="parameter">比较参数 / Comparison parameter</param>
        /// <param name="culture">文化信息 / Culture info</param>
        /// <returns>比较结果 / Comparison result</returns>
        public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value == null || parameter == null)
                return false;

            var enumValue = value.ToString();
            var targetValue = parameter.ToString();

            // 忽略大小写比较枚举值 / Compare enum values ignoring case
            return enumValue?.Equals(targetValue, StringComparison.OrdinalIgnoreCase) == true;
        }

        /// <summary>
        /// 反向转换方法 / Convert Back Method
        /// 此转换器不支持反向转换
        /// This converter does not support reverse conversion
        /// </summary>
        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
