// 引入必要的命名空间 / Import necessary namespaces
using System.Globalization; // 全球化支持 / Globalization support

namespace Scheduler.Converters
{
    /// <summary>
    /// 非空判断转换器 / Is Not Null Converter
    /// 判断对象是否不为空的转换器
    /// Converter that determines if an object is not null
    /// </summary>
    public class IsNotNullConverter : IValueConverter
    {
        /// <summary>
        /// 将对象转换为布尔值 / Convert Object to Boolean
        /// 检查对象是否不为空
        /// Checks if the object is not null
        /// </summary>
        /// <param name="value">要检查的对象 / Object to check</param>
        /// <param name="targetType">目标类型 / Target type</param>
        /// <param name="parameter">参数（未使用）/ Parameter (unused)</param>
        /// <param name="culture">文化信息 / Culture info</param>
        /// <returns>如果对象不为空返回true，否则返回false / Returns true if object is not null, otherwise false</returns>
        public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            return value != null; // 直接返回非空判断结果 / Directly return null check result
        }

        /// <summary>
        /// 反向转换方法 / Convert Back Method
        /// 此转换器不支持反向转换
        /// This converter does not support reverse conversion
        /// </summary>
        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
